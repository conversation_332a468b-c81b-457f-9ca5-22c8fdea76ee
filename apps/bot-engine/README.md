# Bot Engine - SessionManager Implementation

## Overview

The SessionManager is a comprehensive session management system for the WhatsApp Website Bot that handles session lifecycle, persistence, and conversation state management. It provides robust session handling with file-based persistence and real-time conversation tracking.

## Features

- **Session Lifecycle Management**: Create, start, update, complete, fail, and timeout sessions
- **File-based Persistence**: Sessions are stored in JSON files with organized directory structure
- **Conversation Context**: Track WhatsApp conversation state and message history
- **User Management**: Associate sessions with WhatsApp users by phone number
- **Real-time Updates**: Support for streaming communication with narrative items
- **Error Handling**: Robust error handling with recovery mechanisms
- **Session Cleanup**: Automatic cleanup of expired sessions
- **Statistics**: Session statistics and monitoring capabilities

## Architecture

### Storage Structure
```
/app/sessions/
├── users/
│   └── {phoneNumber}/
│       └── sessions.json
├── active/
│   └── {sessionId}.json
└── archive/
    └── {sessionId}.json
```

### Session States
- `created`: Session is initialized but not started
- `active`: Session is currently running
- `completed`: Session finished successfully
- `failed`: Session encountered an error
- `timeout`: Session exceeded time limit

### Session Operations
- `create`: Creating a new website
- `edit`: Editing an existing website
- `import`: Importing from GitHub repository
- `scaffold`: Using scaffold mode

## Usage

### Basic Usage

```typescript
import { SessionManager } from './session-manager.js';

// Initialize SessionManager
const sessionManager = new SessionManager('/app/sessions');

// Create a user
const user = {
  phoneNumber: '+1234567890',
  name: 'John Doe',
  language: 'en',
  preferences: {
    language: 'javascript',
    framework: 'react'
  }
};

// Create a workspace
const workspace = {
  path: '/app/workspace/my-project',
  isGitWorktree: false
};

// Create a session
const session = await sessionManager.createSession({
  user,
  operation: 'create',
  workspace,
  context: {
    messages: [],
    intent: 'create_website'
  }
});

// Start the session
session.start();

// Add messages and narrative items
session.addMessage({
  role: 'user',
  content: 'I want to create a React website'
});

session.addNarrativeItem({
  type: 'text',
  content: 'Processing user request...'
});

// Update session
await sessionManager.updateSession(session);

// Complete session
session.complete();
await sessionManager.updateSession(session);
```

### Advanced Features

#### Session Retrieval
```typescript
// Get session by ID
const session = await sessionManager.getSession(sessionId);

// Get all sessions for a user
const userSessions = await sessionManager.getSessionsByUser(phoneNumber);

// Get active sessions
const activeSessions = await sessionManager.getActiveSessions();

// Get active session for user
const activeSession = await sessionManager.getActiveSession(phoneNumber);
```

#### Message Management
```typescript
// Add narrative item to session
await sessionManager.addMessage(sessionId, {
  type: 'tool_call',
  tool: 'create_file',
  content: 'Creating new file...',
  timestamp: Date.now()
});

// Get all messages from session
const messages = await sessionManager.getSessionMessages(sessionId);
```

#### Session Cleanup
```typescript
// Manual cleanup of expired sessions
await sessionManager.cleanupExpiredSessions();

// Delete specific session
await sessionManager.deleteSession(sessionId);
```

#### Statistics
```typescript
// Get session statistics
const stats = await sessionManager.getSessionStats();
console.log(`Total: ${stats.total}, Active: ${stats.active}, Completed: ${stats.completed}`);
```

## Configuration

### Constructor Options
```typescript
new SessionManager(baseDir?: string)
```

- `baseDir`: Base directory for session storage (default: '/app/sessions')

### Session Options
```typescript
interface SessionOptions {
  id?: string;                    // Optional session ID
  user: User;                     // User information
  operation: SessionOperation;    // Session operation type
  workspace: Workspace;           // Workspace configuration
  context?: SessionContext;       // Initial context
  timeout?: number;               // Timeout in milliseconds
  process?: any;                  // Process reference
  startedAt?: Date;              // Start time
  endedAt?: Date | null;         // End time
  exitCode?: number | null;      // Exit code
  error?: string | null;         // Error message
  lastResponse?: string;         // Last response
  streaming?: StreamingState;    // Streaming state
}
```

## Error Handling

The SessionManager includes comprehensive error handling:

```typescript
try {
  const session = await sessionManager.createSession(options);
  // Handle success
} catch (error) {
  console.error('Failed to create session:', error);
  // Handle error
}
```

### Common Error Scenarios
- Session not found
- Invalid session state transitions
- File system errors
- Timeout handling
- Concurrent access issues

## Performance Considerations

- **Caching**: In-memory cache for frequently accessed sessions
- **Lazy Loading**: Sessions are loaded on-demand
- **Cleanup**: Automatic cleanup of expired sessions
- **File System**: Efficient file-based persistence
- **Memory Management**: Cache size limits to prevent memory leaks

## Integration with Bot Engine

The SessionManager integrates seamlessly with the bot engine:

```typescript
// In your bot engine
import { SessionManager } from './session-manager.js';

class BotEngine {
  private sessionManager: SessionManager;
  
  constructor() {
    this.sessionManager = new SessionManager();
  }
  
  async handleWhatsAppMessage(message: WhatsAppMessage) {
    // Get or create session for user
    let session = await this.sessionManager.getActiveSession(message.From);
    
    if (!session) {
      session = await this.sessionManager.createSession({
        user: { phoneNumber: message.From },
        operation: 'create',
        workspace: { path: '/app/workspace', isGitWorktree: false },
        context: { messages: [] }
      });
    }
    
    // Add user message
    session.addMessage({
      role: 'user',
      content: message.Body
    });
    
    // Update session
    await this.sessionManager.updateSession(session);
    
    // Process message and generate response
    // ...
  }
}
```

## Demo

Run the demo to see the SessionManager in action:

```bash
npx tsx src/session-demo.ts
```

This will demonstrate:
- Session creation and management
- Message handling
- State transitions
- Error scenarios
- Cleanup operations
- Statistics reporting

## Dependencies

- Node.js built-in modules: `fs`, `path`
- TypeScript for type safety
- No external dependencies for core functionality

## License

Part of the WhatsApp Website Bot monorepo.