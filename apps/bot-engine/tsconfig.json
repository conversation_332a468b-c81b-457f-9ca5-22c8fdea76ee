{"extends": "../../tsconfig.base.json", "compilerOptions": {"outDir": "./dist", "rootDir": "./src", "noEmit": false, "baseUrl": "../../", "paths": {"@whatsite-bot/core": ["packages/core/dist"], "@whatsite-bot/core/*": ["packages/core/dist/*"], "@whatsite-bot/workspace-manager": ["packages/workspace-manager/dist"], "@whatsite-bot/workspace-manager/*": ["packages/workspace-manager/dist/*"], "@whatsite-bot/gemini-runner": ["packages/gemini-runner/dist"], "@whatsite-bot/gemini-runner/*": ["packages/gemini-runner/dist/*"]}}, "include": ["src/**/*.ts"], "exclude": ["node_modules", "dist"]}