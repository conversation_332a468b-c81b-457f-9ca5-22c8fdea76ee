/**
 * Performance Manager - Central performance monitoring and optimization service
 * Handles performance tracking, metrics collection, and optimization coordination
 */

import { EventEmitter } from 'events';
import { performance } from 'perf_hooks';
import { cpuUsage, memoryUsage } from 'process';

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  // Response time metrics
  responseTime: {
    avg: number;
    min: number;
    max: number;
    p95: number;
    p99: number;
  };

  // Throughput metrics
  throughput: {
    requestsPerSecond: number;
    operationsPerMinute: number;
    messagesPerMinute: number;
  };

  // Resource usage metrics
  resources: {
    memory: {
      used: number;
      total: number;
      percentage: number;
      heapUsed: number;
      heapTotal: number;
    };
    cpu: {
      user: number;
      system: number;
      percentage: number;
    };
  };

  // Error metrics
  errors: {
    rate: number;
    total: number;
    timeouts: number;
    failures: number;
  };

  // Cache metrics
  cache: {
    hitRate: number;
    missRate: number;
    evictions: number;
    size: number;
  };

  // Connection metrics
  connections: {
    active: number;
    poolSize: number;
    queueSize: number;
    utilization: number;
  };
}

/**
 * Performance configuration
 */
export interface PerformanceConfig {
  metricsInterval: number;
  historySize: number;
  alertThresholds: {
    responseTime: number;
    errorRate: number;
    memoryUsage: number;
    cpuUsage: number;
  };
  optimization: {
    enableAutoOptimization: boolean;
    gcInterval: number;
    cacheCleanupInterval: number;
    connectionPoolCleanup: number;
  };
}

/**
 * Performance events
 */
export interface PerformanceEvents {
  'metrics:collected': (metrics: PerformanceMetrics) => void;
  'threshold:exceeded': (metric: string, value: number, threshold: number) => void;
  'optimization:performed': (type: string, result: unknown) => void;
  'alert:triggered': (alert: PerformanceAlert) => void;
}

/**
 * Performance alert
 */
export interface PerformanceAlert {
  type: 'warning' | 'error' | 'critical';
  metric: string;
  value: number;
  threshold: number;
  message: string;
  timestamp: Date;
  recommendations?: string[];
}

/**
 * Operation timer for measuring performance
 */
export class OperationTimer {
  private startTime: number;
  private endTime?: number;
  private operation: string;

  constructor(operation: string) {
    this.operation = operation;
    this.startTime = performance.now();
  }

  end(): number {
    this.endTime = performance.now();
    return this.endTime - this.startTime;
  }

  getDuration(): number {
    const endTime = this.endTime || performance.now();
    return endTime - this.startTime;
  }

  getOperation(): string {
    return this.operation;
  }
}

/**
 * Performance data collector
 */
class PerformanceDataCollector {
  private responseTimes: number[] = [];
  private requestCount: number = 0;
  private errorCount: number = 0;
  private timeoutCount: number = 0;
  private lastResetTime: number = Date.now();
  private operationCounts: Map<string, number> = new Map();

  recordResponseTime(duration: number): void {
    this.responseTimes.push(duration);
    this.requestCount++;

    // Keep only last 1000 measurements
    if (this.responseTimes.length > 1000) {
      this.responseTimes.shift();
    }
  }

  recordError(isTimeout: boolean = false): void {
    this.errorCount++;
    if (isTimeout) {
      this.timeoutCount++;
    }
  }

  recordOperation(operation: string): void {
    const current = this.operationCounts.get(operation) || 0;
    this.operationCounts.set(operation, current + 1);
  }

  getMetrics(): Partial<PerformanceMetrics> {
    const now = Date.now();
    const timePeriod = (now - this.lastResetTime) / 1000; // seconds

    const sortedTimes = [...this.responseTimes].sort((a, b) => a - b);
    const avg = sortedTimes.reduce((sum, time) => sum + time, 0) / sortedTimes.length || 0;
    const min = sortedTimes[0] || 0;
    const max = sortedTimes[sortedTimes.length - 1] || 0;
    const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)] || 0;
    const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)] || 0;

    return {
      responseTime: { avg, min, max, p95, p99 },
      throughput: {
        requestsPerSecond: this.requestCount / timePeriod,
        operationsPerMinute: (this.requestCount / timePeriod) * 60,
        messagesPerMinute: ((this.operationCounts.get('message') || 0) / timePeriod) * 60,
      },
      errors: {
        rate: this.errorCount / this.requestCount || 0,
        total: this.errorCount,
        timeouts: this.timeoutCount,
        failures: this.errorCount - this.timeoutCount,
      },
    };
  }

  reset(): void {
    this.responseTimes = [];
    this.requestCount = 0;
    this.errorCount = 0;
    this.timeoutCount = 0;
    this.lastResetTime = Date.now();
    this.operationCounts.clear();
  }
}

/**
 * Performance Manager implementation
 */
export class PerformanceManager extends EventEmitter {
  private config: PerformanceConfig;
  private dataCollector: PerformanceDataCollector;
  private metricsInterval?: NodeJS.Timeout;
  private optimizationInterval?: NodeJS.Timeout;
  private isRunning: boolean = false;
  private metricsHistory: PerformanceMetrics[] = [];
  private activeOperations: Map<string, OperationTimer> = new Map();
  private lastCpuUsage = cpuUsage();

  constructor(config: PerformanceConfig) {
    super();
    this.config = config;
    this.dataCollector = new PerformanceDataCollector();
  }

  /**
   * Start performance monitoring
   */
  start(): void {
    if (this.isRunning) {
      return;
    }

    this.isRunning = true;

    // Start metrics collection
    this.metricsInterval = setInterval(() => {
      this.collectMetrics();
    }, this.config.metricsInterval);

    // Start optimization routines
    if (this.config.optimization.enableAutoOptimization) {
      this.optimizationInterval = setInterval(() => {
        this.performOptimizations();
      }, this.config.optimization.gcInterval);
    }

    console.log('[PerformanceManager] Started performance monitoring');
  }

  /**
   * Stop performance monitoring
   */
  stop(): void {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;

    if (this.metricsInterval) {
      clearInterval(this.metricsInterval);
    }

    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    console.log('[PerformanceManager] Stopped performance monitoring');
  }

  /**
   * Start timing an operation
   */
  startOperation(operation: string): OperationTimer {
    const timer = new OperationTimer(operation);
    this.activeOperations.set(operation, timer);
    return timer;
  }

  /**
   * End timing an operation
   */
  endOperation(operation: string): number {
    const timer = this.activeOperations.get(operation);
    if (!timer) {
      return 0;
    }

    const duration = timer.end();
    this.activeOperations.delete(operation);

    this.dataCollector.recordResponseTime(duration);
    this.dataCollector.recordOperation(operation);

    return duration;
  }

  /**
   * Record an error
   */
  recordError(operation: string, error: Error, isTimeout: boolean = false): void {
    this.dataCollector.recordError(isTimeout);

    console.error(`[PerformanceManager] Error in ${operation}:`, error);

    // Check if we should trigger an alert
    this.checkThresholds();
  }

  /**
   * Get current metrics
   */
  getCurrentMetrics(): PerformanceMetrics {
    const basicMetrics = this.dataCollector.getMetrics();
    const memStats = memoryUsage();
    const cpuStats = cpuUsage(this.lastCpuUsage);
    this.lastCpuUsage = cpuUsage();

    const metrics: PerformanceMetrics = {
      ...basicMetrics,
      resources: {
        memory: {
          used: memStats.rss,
          total: memStats.heapTotal + memStats.external,
          percentage: (memStats.rss / (memStats.heapTotal + memStats.external)) * 100,
          heapUsed: memStats.heapUsed,
          heapTotal: memStats.heapTotal,
        },
        cpu: {
          user: cpuStats.user,
          system: cpuStats.system,
          percentage: ((cpuStats.user + cpuStats.system) / 1000000) * 100,
        },
      },
      cache: {
        hitRate: 0, // Will be populated by cache service
        missRate: 0,
        evictions: 0,
        size: 0,
      },
      connections: {
        active: 0, // Will be populated by connection pool
        poolSize: 0,
        queueSize: 0,
        utilization: 0,
      },
    } as PerformanceMetrics;

    return metrics;
  }

  /**
   * Get metrics history
   */
  getMetricsHistory(): PerformanceMetrics[] {
    return [...this.metricsHistory];
  }

  /**
   * Get performance summary
   */
  getPerformanceSummary(): any {
    const current = this.getCurrentMetrics();
    const history = this.metricsHistory.slice(-10); // Last 10 measurements

    return {
      current,
      trends: {
        responseTime: this.calculateTrend(history.map(m => m.responseTime.avg)),
        throughput: this.calculateTrend(history.map(m => m.throughput.requestsPerSecond)),
        errorRate: this.calculateTrend(history.map(m => m.errors.rate)),
        memoryUsage: this.calculateTrend(history.map(m => m.resources.memory.percentage)),
      },
      recommendations: this.generateRecommendations(current),
    };
  }

  /**
   * Update cache metrics
   */
  updateCacheMetrics(metrics: PerformanceMetrics['cache']): void {
    const current = this.getCurrentMetrics();
    current.cache = metrics;
  }

  /**
   * Update connection metrics
   */
  updateConnectionMetrics(metrics: PerformanceMetrics['connections']): void {
    const current = this.getCurrentMetrics();
    current.connections = metrics;
  }

  /**
   * Collect metrics
   */
  private collectMetrics(): void {
    const metrics = this.getCurrentMetrics();

    // Add to history
    this.metricsHistory.push(metrics);

    // Keep history size limited
    if (this.metricsHistory.length > this.config.historySize) {
      this.metricsHistory.shift();
    }

    // Emit metrics event
    this.emit('metrics:collected', metrics);

    // Check thresholds
    this.checkThresholds();
  }

  /**
   * Check performance thresholds
   */
  private checkThresholds(): void {
    const metrics = this.getCurrentMetrics();

    const checks = [
      {
        metric: 'responseTime',
        value: metrics.responseTime.avg,
        threshold: this.config.alertThresholds.responseTime,
      },
      {
        metric: 'errorRate',
        value: metrics.errors.rate * 100,
        threshold: this.config.alertThresholds.errorRate,
      },
      {
        metric: 'memoryUsage',
        value: metrics.resources.memory.percentage,
        threshold: this.config.alertThresholds.memoryUsage,
      },
      {
        metric: 'cpuUsage',
        value: metrics.resources.cpu.percentage,
        threshold: this.config.alertThresholds.cpuUsage,
      },
    ];

    for (const check of checks) {
      if (check.value > check.threshold) {
        this.emit('threshold:exceeded', check.metric, check.value, check.threshold);

        const alert: PerformanceAlert = {
          type: this.getAlertType(check.metric, check.value, check.threshold),
          metric: check.metric,
          value: check.value,
          threshold: check.threshold,
          message: `${check.metric} (${check.value.toFixed(2)}) exceeded threshold (${check.threshold})`,
          timestamp: new Date(),
          recommendations: this.getRecommendations(check.metric, check.value),
        };

        this.emit('alert:triggered', alert);
      }
    }
  }

  /**
   * Perform optimizations
   */
  private performOptimizations(): void {
    const metrics = this.getCurrentMetrics();

    // Trigger garbage collection if memory usage is high
    if (metrics.resources.memory.percentage > 80) {
      if (global.gc) {
        global.gc();
        this.emit('optimization:performed', 'garbage_collection', {
          memoryBefore: metrics.resources.memory.percentage,
        });
      }
    }

    // Clear old metrics if history is too large
    if (this.metricsHistory.length > this.config.historySize * 1.5) {
      const removed = this.metricsHistory.splice(
        0,
        this.metricsHistory.length - this.config.historySize,
      );
      this.emit('optimization:performed', 'metrics_cleanup', { removed: removed.length });
    }
  }

  /**
   * Calculate trend from values
   */
  private calculateTrend(values: number[]): 'up' | 'down' | 'stable' {
    if (values.length < 2) return 'stable';

    const recent = values.slice(-3);
    const older = values.slice(-6, -3);

    if (recent.length === 0 || older.length === 0) return 'stable';

    const recentAvg = recent.reduce((sum, val) => sum + val, 0) / recent.length;
    const olderAvg = older.reduce((sum, val) => sum + val, 0) / older.length;

    const changePercent = ((recentAvg - olderAvg) / olderAvg) * 100;

    if (changePercent > 5) return 'up';
    if (changePercent < -5) return 'down';
    return 'stable';
  }

  /**
   * Generate recommendations based on metrics
   */
  private generateRecommendations(metrics: PerformanceMetrics): string[] {
    const recommendations: string[] = [];

    if (metrics.responseTime.avg > 2000) {
      recommendations.push('Consider implementing connection pooling for external APIs');
      recommendations.push('Add caching for frequently accessed data');
    }

    if (metrics.errors.rate > 0.05) {
      recommendations.push('Implement circuit breaker pattern for external services');
      recommendations.push('Add retry logic with exponential backoff');
    }

    if (metrics.resources.memory.percentage > 80) {
      recommendations.push('Enable garbage collection optimization');
      recommendations.push('Implement object pooling for frequently created objects');
    }

    if (metrics.resources.cpu.percentage > 70) {
      recommendations.push('Consider offloading CPU-intensive tasks to worker threads');
      recommendations.push('Implement request queuing to prevent CPU overload');
    }

    return recommendations;
  }

  /**
   * Get alert type based on severity
   */
  private getAlertType(
    _metric: string,
    value: number,
    threshold: number,
  ): PerformanceAlert['type'] {
    const ratio = value / threshold;

    if (ratio > 2) return 'critical';
    if (ratio > 1.5) return 'error';
    return 'warning';
  }

  /**
   * Get recommendations for specific metric
   */
  private getRecommendations(metric: string, _value: number): string[] {
    switch (metric) {
      case 'responseTime':
        return [
          'Implement connection pooling',
          'Add response caching',
          'Optimize database queries',
          'Use CDN for static assets',
        ];
      case 'errorRate':
        return [
          'Implement circuit breaker',
          'Add retry logic',
          'Improve error handling',
          'Monitor external service health',
        ];
      case 'memoryUsage':
        return [
          'Enable garbage collection',
          'Implement object pooling',
          'Reduce memory leaks',
          'Optimize data structures',
        ];
      case 'cpuUsage':
        return [
          'Use worker threads',
          'Implement request queuing',
          'Optimize algorithms',
          'Cache computed results',
        ];
      default:
        return [];
    }
  }
}

/**
 * Create performance manager instance
 */
export function createPerformanceManager(config: PerformanceConfig): PerformanceManager {
  return new PerformanceManager(config);
}

/**
 * Default performance manager configuration
 */
export const DEFAULT_PERFORMANCE_CONFIG: PerformanceConfig = {
  metricsInterval: 30000, // 30 seconds
  historySize: 100,
  alertThresholds: {
    responseTime: 2000, // 2 seconds
    errorRate: 5, // 5%
    memoryUsage: 85, // 85%
    cpuUsage: 80, // 80%
  },
  optimization: {
    enableAutoOptimization: true,
    gcInterval: 60000, // 1 minute
    cacheCleanupInterval: 300000, // 5 minutes
    connectionPoolCleanup: 120000, // 2 minutes
  },
};
