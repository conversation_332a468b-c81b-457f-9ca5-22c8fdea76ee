/**
 * WebsiteManager implementation for WhatsApp Website Bot
 * Handles website operations including repository management, deployment, and integration
 */

// import { Octokit } from '@octokit/rest';
import { randomUUID } from 'crypto';

import type { StatusMessenger } from '../communication/status-messenger.js';

// Local type definitions to avoid import issues
type SessionState = 'created' | 'active' | 'completed' | 'failed' | 'timeout';
type SessionOperation = 'create' | 'edit' | 'import' | 'scaffold';
type ProjectType =
  | 'static'
  | 'spa'
  | 'react'
  | 'vue'
  | 'angular'
  | 'nextjs'
  | 'nuxt'
  | 'svelte'
  | 'custom';
type WorkspaceType = 'temporary' | 'persistent' | 'template' | 'import';

interface User {
  phoneNumber: string;
  name?: string;
  profilePicture?: string;
  language?: string;
  registeredAt?: Date;
  lastActiveAt?: Date;
  isActive?: boolean;
  preferences?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

interface RepositoryInfo {
  owner?: string;
  repo?: string;
  branch?: string;
  fullUrl?: string;
  gitUrl?: string;
  isPrivate?: boolean;
  defaultBranch?: string;
  description?: string;
  topics?: string[];
  language?: string;
  createdAt?: string;
  updatedAt?: string;
  pushedAt?: string;
  size?: number;
  stargazersCount?: number;
  watchersCount?: number;
  forksCount?: number;
  openIssuesCount?: number;
  license?: string;
  hasIssues?: boolean;
  hasProjects?: boolean;
  hasWiki?: boolean;
  hasPages?: boolean;
  hasDownloads?: boolean;
  archived?: boolean;
  disabled?: boolean;
  visibility?: string;
  permissions?: Record<string, boolean>;
  allowRebaseMerge?: boolean;
  allowSquashMerge?: boolean;
  allowMergeCommit?: boolean;
  deleteBranchOnMerge?: boolean;
  allowForking?: boolean;
  webCommitSignoffRequired?: boolean;
  subscribersCount?: number;
  networkCount?: number;
  tempCloneToken?: string;
  allowAutoMerge?: boolean;
  allowUpdateBranch?: boolean;
  useSquashPrTitleAsDefault?: boolean;
  squashMergeCommitTitle?: string;
  squashMergeCommitMessage?: string;
  mergeCommitTitle?: string;
  mergeCommitMessage?: string;
  customProperties?: Record<string, unknown>;
  organization?: Record<string, unknown>;
  parent?: Record<string, unknown>;
  source?: Record<string, unknown>;
  securityAndAnalysis?: Record<string, unknown>;
  cloneUrl?: string;
  mirrorUrl?: string;
  hooksUrl?: string;
  svnUrl?: string;
  homepage?: string;
  deploymentsUrl?: string;
}

interface SessionMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: Record<string, unknown>;
}

interface SessionContext {
  messages: SessionMessage[];
  intent?: string;
  project?: Record<string, unknown>;
  preferences?: Record<string, unknown>;
  data?: Record<string, unknown>;
}

interface NarrativeItem {
  type: 'text' | 'tool_call' | 'file_operation' | 'deployment' | 'error';
  content?: string;
  tool?: string;
  file?: string;
  operation?: 'create' | 'update' | 'delete' | 'read';
  timestamp: number;
  metadata?: Record<string, unknown>;
}

interface StreamingState {
  active: boolean;
  messageId?: string;
  content?: string;
  narrative: NarrativeItem[];
  synthesis?: string;
}

interface WebsiteSession {
  id: string;
  user: User;
  operation: SessionOperation;
  workspace: Workspace;
  state: SessionState;
  context: SessionContext;
  timeoutMs: number;
  process: unknown;
  startedAt: Date;
  endedAt: Date | null;
  exitCode: number | null;
  error: string | null;
  lastResponse: string;
  streaming: StreamingState;
  addNarrativeItem(item: Omit<NarrativeItem, 'timestamp'>): void;
}

interface TechStack {
  framework?: string;
  styling?: string;
  buildTool?: string;
  packageManager?: string;
  runtime?: string;
  dependencies?: string[];
}

interface ProjectConfig {
  srcDir: string;
  buildDir: string;
  publicDir: string;
  entryPoint: string;
  configFiles?: string[];
  env?: Record<string, string>;
}

interface BuildConfig {
  command: string;
  outputDir: string;
  env?: Record<string, string>;
  options?: Record<string, unknown>;
}

interface DeploymentConfig {
  platform: string;
  config: Record<string, unknown>;
  env?: Record<string, string>;
  domain?: string;
}

interface WorkspaceProject {
  name: string;
  description?: string;
  type: ProjectType;
  stack: TechStack;
  config: ProjectConfig;
  build?: BuildConfig;
  deployment?: DeploymentConfig;
}

interface Workspace {
  id: string;
  path: string;
  isGitWorktree: boolean;
  basePath?: string;
  branch?: string;
  historyPath?: string;
  type: WorkspaceType;
  user: User;
  project?: WorkspaceProject;
  createdAt: Date;
  lastAccessedAt: Date;
  isActive: boolean;
  metadata?: Record<string, unknown>;
}

interface WorkspaceFile {
  name: string;
  path: string;
  type: 'file' | 'directory';
  size?: number;
  lastModified: Date;
  permissions?: string;
  isBinary?: boolean;
}

interface WorkspaceManager {
  createWorkspace(options: Record<string, unknown>): Promise<Workspace>;
  getWorkspace(id: string): Promise<Workspace | null>;
  listWorkspaceFiles(workspace: Workspace, directory?: string): Promise<WorkspaceFile[]>;
  readWorkspaceFile(workspace: Workspace, filePath: string): Promise<string>;
  writeWorkspaceFile(workspace: Workspace, filePath: string, content: string): Promise<void>;
  cleanupWorkspace(workspace: Workspace): Promise<void>;
}

interface CodeGenerationResult {
  success: boolean;
  error?: string;
  files?: Array<{ path: string; content: string }>;
  code?: string;
}

interface GeminiRunner {
  initialize(workspace: Workspace): Promise<void>;
  generateCode(prompt: string, context: Record<string, unknown>): Promise<CodeGenerationResult>;
  updateCode(filePath: string, content: string, instructions: string): Promise<string>;
}

interface DeploymentResult {
  success: boolean;
  url?: string;
  logs?: string[];
  errors?: string[];
  duration: number;
  metadata?: Record<string, unknown>;
}

// interface BuildResult {
//   success: boolean;
//   output?: string;
//   errors?: string[];
//   warnings?: string[];
//   duration: number;
//   files?: string[];
// }

interface ProjectStatus {
  initialized: boolean;
  buildable: boolean;
  deployable: boolean;
  missingDependencies?: string[];
  configIssues?: string[];
}

// interface GitStatus {
//   branch?: string;
//   remoteUrl?: string;
//   isClean: boolean;
//   modifiedFiles?: string[];
//   untrackedFiles?: string[];
//   stagedFiles?: string[];
//   latestCommit?: string;
// }

interface RepositoryValidation {
  isValid: boolean;
  error?: string;
  repoData?: Record<string, unknown>;
}

interface RepositoryMetadata {
  fullName: string;
  name: string;
  description: string;
  htmlUrl: string;
  cloneUrl: string;
  defaultBranch: string;
  createdAt: string;
  updatedAt: string;
  latestCommitSha: string | null;
}

// Error classes
class BotError extends Error {
  public readonly code: string;
  public readonly category: string;
  public readonly severity: 'low' | 'medium' | 'high' | 'critical';
  public readonly context?: Record<string, unknown>;
  public readonly timestamp: Date;
  public readonly retryable: boolean;
  public readonly userMessage?: string;

  constructor(
    message: string,
    code: string,
    category: string,
    severity: 'low' | 'medium' | 'high' | 'critical' = 'medium',
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message);
    this.name = this.constructor.name;
    this.code = code;
    this.category = category;
    this.severity = severity;
    this.context = context;
    this.timestamp = new Date();
    this.retryable = retryable;
    this.userMessage = userMessage;
  }

  getUserMessage(): string {
    return this.userMessage || 'An error occurred. Please try again.';
  }
}

class WorkspaceError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'workspace', 'medium', context, retryable, userMessage);
  }
}

class GitError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'git', 'medium', context, retryable, userMessage);
  }
}

class DeploymentError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'deployment', 'medium', context, retryable, userMessage);
  }
}

class AIError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'ai', 'medium', context, retryable, userMessage);
  }
}

class ValidationError extends BotError {
  constructor(
    message: string,
    code: string,
    context?: Record<string, unknown>,
    retryable: boolean = false,
    userMessage?: string,
  ) {
    super(message, code, 'validation', 'low', context, retryable, userMessage);
  }
}

// class ExternalServiceError extends BotError {
//   constructor(
//     message: string,
//     code: string,
//     context?: Record<string, unknown>,
//     retryable: boolean = false,
//     userMessage?: string
//   ) {
//     super(message, code, 'external_service', 'critical', context, retryable, userMessage);
//   }
// }

class ErrorUtils {
  static fromUnknown(
    error: unknown,
    defaultMessage: string = 'An unknown error occurred',
  ): BotError {
    if (error instanceof BotError) {
      return error;
    }

    if (error instanceof Error) {
      return new BotError(
        error.message || defaultMessage,
        'UNKNOWN_ERROR',
        'system',
        'medium',
        { originalError: error.name, stack: error.stack },
        true,
        'An unexpected error occurred. Please try again.',
      );
    }

    return new BotError(
      defaultMessage,
      'UNKNOWN_ERROR',
      'system',
      'medium',
      { originalError: error },
      true,
      'An unexpected error occurred. Please try again.',
    );
  }
}

/**
 * File operation for repository updates
 */
export interface FileOperation {
  /** File path */
  path: string;
  /** File content */
  content?: string;
  /** Operation type */
  operation: 'create' | 'update' | 'delete';
  /** File encoding */
  encoding?: 'utf-8' | 'base64';
}

/**
 * Import result
 */
export interface ImportResult {
  /** Success status */
  success: boolean;
  /** Project name */
  projectName: string;
  /** Repository URL */
  repositoryUrl: string;
  /** Deployment URL */
  deploymentUrl: string;
  /** Import description */
  description?: string;
  /** Error message */
  error?: string;
}

/**
 * Website creation context
 */
export interface WebsiteCreationContext {
  /** User prompt */
  prompt: string;
  /** Project name */
  projectName: string;
  /** User preferences */
  preferences?: Record<string, unknown>;
  /** Technology stack */
  stack?: string[];
  /** Features to include */
  features?: string[];
}

/**
 * Website edit context
 */
export interface WebsiteEditContext {
  /** Edit instructions */
  instructions: string;
  /** Current content */
  currentContent?: string;
  /** Target files */
  targetFiles?: string[];
  /** Edit type */
  editType?: 'code' | 'content' | 'structure';
}

/**
 * GitHub configuration
 */
export interface GitHubConfig {
  /** GitHub token */
  token: string;
  /** Default organization */
  organization?: string;
  /** Base URL for API */
  baseUrl?: string;
}

/**
 * Vercel configuration
 */
export interface VercelConfig {
  /** Vercel token */
  token: string;
  /** Team ID */
  teamId?: string;
  /** Project ID */
  projectId?: string;
}

/**
 * WebsiteManager configuration
 */
export interface WebsiteManagerConfig {
  /** GitHub configuration */
  github: GitHubConfig;
  /** Vercel configuration */
  vercel: VercelConfig;
  /** Default workspace directory */
  workspaceDir: string;
  /** Session timeout */
  sessionTimeout: number;
  /** Enable git integration */
  enableGitIntegration: boolean;
  /** Enable deployment */
  enableDeployment: boolean;
}

/**
 * WebsiteManager class for managing website operations
 */
export class WebsiteManager {
  private config: WebsiteManagerConfig;
  private workspaceManager: WorkspaceManager;
  private geminiRunner: GeminiRunner;
  private github: {
    repos: {
      createForAuthenticatedUser: (
        params: unknown,
      ) => Promise<{ data: { clone_url: string; html_url: string } }>;
    };
    git: {
      getTree: (
        params: unknown,
      ) => Promise<{ data: { tree: Array<{ type: string; path?: string; sha?: string }> } }>;
      getBlob: (params: unknown) => Promise<{ data: { content: string } }>;
      getRef: (params: unknown) => Promise<{ data: { object: { sha: string } } }>;
      getCommit: (params: unknown) => Promise<{ data: { tree: { sha: string } } }>;
      createTree: (params: unknown) => Promise<{ data: { sha: string } }>;
      createCommit: (params: unknown) => Promise<{ data: { sha: string } }>;
      createBlob: (params: unknown) => Promise<{ data: { sha: string } }>;
      updateRef: (params: unknown) => Promise<unknown>;
    };
  } | null = null;
  private activeOperations: Map<string, AbortController> = new Map();
  private statusMessenger?: StatusMessenger;

  constructor(
    config: WebsiteManagerConfig,
    workspaceManager: WorkspaceManager,
    geminiRunner: GeminiRunner,
    statusMessenger?: StatusMessenger,
  ) {
    this.config = config;
    this.workspaceManager = workspaceManager;
    this.geminiRunner = geminiRunner;
    this.statusMessenger = statusMessenger;

    // Initialize GitHub client
    this.github = null; // new Octokit({
    //   auth: config.github.token,
    //   baseUrl: config.github.baseUrl
    // });

    console.log('[WebsiteManager] Initialized with configuration:', {
      gitIntegration: config.enableGitIntegration,
      deployment: config.enableDeployment,
      workspaceDir: config.workspaceDir,
      statusMessaging: !!statusMessenger,
    });
  }

  /**
   * Create a new website
   */
  async createWebsite(session: WebsiteSession): Promise<DeploymentResult> {
    const startTime = Date.now();
    const operationId = randomUUID();
    const abortController = new AbortController();

    try {
      this.activeOperations.set(operationId, abortController);

      // Add narrative item
      session.addNarrativeItem({
        type: 'text',
        content: 'Starting website creation...',
      });

      // Send status update
      if (this.statusMessenger) {
        await this.statusMessenger.sendOperationStart(session.user, 'create_website', {
          projectName: session.workspace.project?.name || 'Website',
        });
      }

      // Extract creation context from session
      const context = this.extractCreationContext(session);

      // Create workspace
      const workspace = await this.createWorkspace(session, context);

      // Update session with workspace
      session.context.data = {
        ...session.context.data,
        workspace: workspace,
        operationId,
      };

      // Initialize Gemini runner
      await this.geminiRunner.initialize(workspace);

      // Generate website code
      session.addNarrativeItem({
        type: 'text',
        content: 'Generating website code...',
      });

      const codeGeneration = await this.geminiRunner.generateCode(context.prompt, {
        project: workspace.project!,
        workspace: workspace,
        files: [],
        requirements: context.prompt,
        constraints: context.stack,
      });

      if (!codeGeneration.success) {
        throw new AIError(
          `Code generation failed: ${codeGeneration.error}`,
          'CODE_GENERATION_FAILED',
          { context },
        );
      }

      // Write generated files to workspace
      if (codeGeneration.files) {
        await this.writeFilesToWorkspace(workspace, codeGeneration.files);
      }

      // Create GitHub repository if enabled
      let repositoryUrl: string | null = null;
      if (this.config.enableGitIntegration) {
        session.addNarrativeItem({
          type: 'text',
          content: 'Creating GitHub repository...',
        });

        // Send GitHub creation status
        if (this.statusMessenger) {
          await this.statusMessenger.sendStatusMessage({
            user: session.user,
            type: 'github_creation_start',
            data: { projectName: context.projectName },
          });
        }

        repositoryUrl = await this.createGitHubRepository(
          context.projectName,
          `Website created for ${session.user.name || session.user.phoneNumber}`,
        );

        // Commit initial code
        if (codeGeneration.files) {
          await this.commitInitialCode(repositoryUrl, codeGeneration.files);
        }

        // Send GitHub creation complete status
        if (this.statusMessenger) {
          await this.statusMessenger.sendStatusMessage({
            user: session.user,
            type: 'github_creation_complete',
            data: { projectName: context.projectName, repoUrl: repositoryUrl },
          });
        }
      }

      // Deploy to Vercel
      let deploymentUrl: string | null = null;
      if (this.config.enableDeployment) {
        session.addNarrativeItem({
          type: 'deployment',
          content: 'Deploying to Vercel...',
        });

        // Send deployment status
        if (this.statusMessenger) {
          await this.statusMessenger.sendStatusMessage({
            user: session.user,
            type: 'deployment_start',
            data: { projectName: context.projectName },
          });
        }

        if (codeGeneration.code) {
          deploymentUrl = await this.deployToVercel(codeGeneration.code, context.projectName);
        }

        // Send deployment complete status
        if (this.statusMessenger) {
          await this.statusMessenger.sendStatusMessage({
            user: session.user,
            type: 'deployment_complete',
            data: { projectName: context.projectName, url: deploymentUrl },
          });
        }
      }

      // Update session with results
      session.context.project = {
        name: context.projectName,
        description: context.prompt,
        url: deploymentUrl,
        repository: repositoryUrl
          ? {
              url: repositoryUrl,
              name: context.projectName,
              owner: this.config.github.organization || 'user',
              branch: 'main',
            }
          : undefined,
      };

      session.addNarrativeItem({
        type: 'text',
        content: 'Website created successfully!',
      });

      // Send operation success status
      if (this.statusMessenger) {
        await this.statusMessenger.sendOperationSuccess(session.user, 'create_website', {
          projectName: context.projectName,
          url: deploymentUrl,
          repoUrl: repositoryUrl,
          timeElapsed: Math.round((Date.now() - startTime) / 1000),
        });
      }

      return {
        success: true,
        url: deploymentUrl || undefined,
        duration: Date.now() - startTime,
        metadata: {
          projectName: context.projectName,
          repositoryUrl,
          workspace: workspace.id,
          operationId,
        },
      };
    } catch (error) {
      const botError = ErrorUtils.fromUnknown(error, 'Website creation failed');

      session.addNarrativeItem({
        type: 'error',
        content: botError.getUserMessage(),
      });

      // Send error status
      if (this.statusMessenger) {
        await this.statusMessenger.sendOperationError(session.user, 'create_website', botError, {
          projectName: session.workspace.project?.name || 'Website',
          timeElapsed: Math.round((Date.now() - startTime) / 1000),
        });
      }

      // Clean up on error
      await this.cleanupOperation(operationId, session);

      return {
        success: false,
        errors: [botError.message],
        duration: Date.now() - startTime,
        metadata: { operationId },
      };
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Edit existing website
   */
  async editWebsite(session: WebsiteSession): Promise<DeploymentResult> {
    const startTime = Date.now();
    const operationId = randomUUID();
    const abortController = new AbortController();

    try {
      this.activeOperations.set(operationId, abortController);

      session.addNarrativeItem({
        type: 'text',
        content: 'Starting website edit...',
      });

      // Extract edit context from session
      const context = this.extractEditContext(session);

      // Get or create workspace
      const workspace = await this.getOrCreateWorkspace(session);

      // Update session with workspace
      session.context.data = {
        ...session.context.data,
        workspace: workspace,
        operationId,
      };

      // Initialize Gemini runner
      await this.geminiRunner.initialize(workspace);

      // Get current files
      const currentFiles = await this.workspaceManager.listWorkspaceFiles(workspace);

      // Get current content if available
      let currentContent = '';
      if (context.currentContent) {
        currentContent = context.currentContent;
      } else if (currentFiles.length > 0) {
        // Get main file content
        const mainFile =
          currentFiles.find(
            (f: WorkspaceFile) => f.name.includes('index') || f.name.includes('main'),
          ) || currentFiles[0];

        if (mainFile && mainFile.type === 'file') {
          currentContent = await this.workspaceManager.readWorkspaceFile(workspace, mainFile.path);
        }
      }

      // Generate updated code
      session.addNarrativeItem({
        type: 'text',
        content: 'Updating website code...',
      });

      const updatedCode = await this.geminiRunner.updateCode(
        'index.html',
        currentContent,
        context.instructions,
      );

      // Write updated files to workspace
      await this.workspaceManager.writeWorkspaceFile(workspace, 'index.html', updatedCode);

      // Update repository if it exists
      if (session.context.project?.repository) {
        session.addNarrativeItem({
          type: 'text',
          content: 'Updating repository...',
        });

        await this.updateRepository(session.context.project.repository.url, [
          {
            path: 'index.html',
            content: updatedCode,
            operation: 'update',
          },
        ]);
      }

      // Redeploy to Vercel
      let deploymentUrl: string | null = null;
      if (this.config.enableDeployment) {
        session.addNarrativeItem({
          type: 'deployment',
          content: 'Redeploying to Vercel...',
        });

        deploymentUrl = await this.deployToVercel(
          updatedCode,
          session.context.project?.name || 'website',
        );
      }

      // Update session with results
      if (session.context.project) {
        session.context.project.url = deploymentUrl;
      }

      session.addNarrativeItem({
        type: 'text',
        content: 'Website updated successfully!',
      });

      return {
        success: true,
        url: deploymentUrl || undefined,
        duration: Date.now() - startTime,
        metadata: {
          projectName: session.context.project?.name,
          workspace: workspace.id,
          operationId,
        },
      };
    } catch (error) {
      const botError = ErrorUtils.fromUnknown(error, 'Website edit failed');

      session.addNarrativeItem({
        type: 'error',
        content: botError.getUserMessage(),
      });

      // Clean up on error
      await this.cleanupOperation(operationId, session);

      return {
        success: false,
        errors: [botError.message],
        duration: Date.now() - startTime,
        metadata: { operationId },
      };
    } finally {
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Import repository
   */
  async importRepository(repoInfo: RepositoryInfo): Promise<ImportResult> {
    try {
      // Validate repository
      const validation = await this.validateRepository(repoInfo);
      if (!validation.isValid) {
        throw new ValidationError(
          `Invalid repository: ${validation.error}`,
          'REPOSITORY_VALIDATION_FAILED',
          { repoInfo },
        );
      }

      // Get repository metadata
      const metadata = await this.getRepositoryMetadata(repoInfo);

      // Clone repository content
      const content = await this.getRepositoryContent(repoInfo);

      // Deploy to Vercel
      let deploymentUrl: string | null = null;
      if (this.config.enableDeployment) {
        deploymentUrl = await this.deployToVercel(content, metadata.name);
      }

      return {
        success: true,
        projectName: metadata.name,
        repositoryUrl: metadata.htmlUrl,
        deploymentUrl: deploymentUrl || metadata.htmlUrl,
        description: metadata.description,
      };
    } catch (error) {
      const botError = ErrorUtils.fromUnknown(error, 'Repository import failed');

      return {
        success: false,
        projectName: repoInfo.repo || 'unknown',
        repositoryUrl: repoInfo.fullUrl || '',
        deploymentUrl: '',
        error: botError.getUserMessage(),
      };
    }
  }

  /**
   * Deploy to Vercel
   */
  async deployToVercel(code: string, projectName: string): Promise<string> {
    try {
      // Prepare deployment payload
      const files = {
        'index.html': code,
        'package.json': JSON.stringify(
          {
            name: projectName,
            version: '1.0.0',
            scripts: {
              build: 'echo "Static site, no build needed"',
              start: 'serve .',
            },
          },
          null,
          2,
        ),
      };

      // Deploy via Vercel API
      const response = await fetch('https://api.vercel.com/v13/deployments', {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${this.config.vercel.token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: projectName,
          files: Object.entries(files).map(([path, content]) => ({
            file: path,
            data: Buffer.from(content).toString('base64'),
          })),
          projectSettings: {
            framework: null,
            buildCommand: null,
            outputDirectory: null,
          },
        }),
      });

      if (!response.ok) {
        throw new DeploymentError(
          `Vercel deployment failed: ${response.statusText}`,
          'VERCEL_DEPLOYMENT_FAILED',
          { status: response.status },
        );
      }

      const deploymentData: unknown = await response.json();
      const deployment = deploymentData as { url: string };

      // Return deployment URL
      return `https://${deployment.url}`;
    } catch (error) {
      throw new DeploymentError(
        `Vercel deployment failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'VERCEL_DEPLOYMENT_FAILED',
        { projectName, error },
      );
    }
  }

  /**
   * Create GitHub repository
   */
  async createGitHubRepository(name: string, description: string): Promise<string> {
    try {
      if (!this.github) {
        throw new GitError('GitHub client not initialized', 'GITHUB_NOT_INITIALIZED');
      }
      const response = await this.github.repos.createForAuthenticatedUser({
        name: name,
        description: description,
        private: false,
        auto_init: true,
      });

      return response.data.html_url;
    } catch (error) {
      throw new GitError(
        `GitHub repository creation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'GITHUB_REPO_CREATION_FAILED',
        { name, description, error },
      );
    }
  }

  /**
   * Clone repository
   */
  async cloneRepository(repositoryUrl: string, workspacePath: string): Promise<void> {
    try {
      if (!this.github) {
        throw new GitError('GitHub client not initialized', 'GITHUB_NOT_INITIALIZED');
      }
      // Extract owner and repo from URL
      const urlParts = repositoryUrl.replace('https://github.com/', '').split('/');
      const owner = urlParts[0];
      const repo = urlParts[1];

      // Get repository content
      const { data: tree } = await this.github.git.getTree({
        owner,
        repo,
        tree_sha: 'main',
        recursive: 'true',
      });

      // Create workspace directory
      const workspace = await this.workspaceManager.createWorkspace({
        user: { phoneNumber: 'system' } as User,
        type: 'import',
        basePath: workspacePath,
      });

      // Download and write files
      for (const item of tree.tree) {
        if (item.type === 'blob' && item.path) {
          const { data: blob } = await this.github.git.getBlob({
            owner,
            repo,
            file_sha: item.sha!,
          });

          const content = Buffer.from(blob.content, 'base64').toString('utf-8');
          await this.workspaceManager.writeWorkspaceFile(workspace, item.path, content);
        }
      }
    } catch (error) {
      throw new GitError(
        `Repository clone failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'REPOSITORY_CLONE_FAILED',
        { repositoryUrl, workspacePath, error },
      );
    }
  }

  /**
   * Update repository
   */
  async updateRepository(repositoryUrl: string, files: FileOperation[]): Promise<void> {
    try {
      if (!this.github) {
        throw new GitError('GitHub client not initialized', 'GITHUB_NOT_INITIALIZED');
      }
      // Extract owner and repo from URL
      const urlParts = repositoryUrl.replace('https://github.com/', '').split('/');
      const owner = urlParts[0];
      const repo = urlParts[1];

      // Get current main branch SHA
      const { data: ref } = await this.github.git.getRef({
        owner,
        repo,
        ref: 'heads/main',
      });

      // Get current tree
      const { data: commit } = await this.github.git.getCommit({
        owner,
        repo,
        commit_sha: ref.object.sha,
      });

      // Create new tree with updated files
      const tree = [];
      for (const file of files) {
        if (file.operation === 'delete') {
          tree.push({
            path: file.path,
            mode: '100644' as const,
            type: 'blob' as const,
            sha: null,
          });
        } else {
          const blob = await this.github.git.createBlob({
            owner,
            repo,
            content: file.content || '',
            encoding: 'utf-8',
          });

          tree.push({
            path: file.path,
            mode: '100644' as const,
            type: 'blob' as const,
            sha: blob.data.sha,
          });
        }
      }

      // Create new tree
      const { data: newTree } = await this.github.git.createTree({
        owner,
        repo,
        tree,
        base_tree: commit.tree.sha,
      });

      // Create new commit
      const { data: newCommit } = await this.github.git.createCommit({
        owner,
        repo,
        message: `Update ${files.length} files`,
        tree: newTree.sha,
        parents: [ref.object.sha],
      });

      // Update reference
      await this.github.git.updateRef({
        owner,
        repo,
        ref: 'heads/main',
        sha: newCommit.sha,
      });
    } catch (error) {
      throw new GitError(
        `Repository update failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'REPOSITORY_UPDATE_FAILED',
        { repositoryUrl, files, error },
      );
    }
  }

  /**
   * Get project status
   */
  getProjectStatus(projectName: string): ProjectStatus {
    try {
      // This is a simplified implementation
      // In a real implementation, this would check various project aspects

      return {
        initialized: true,
        buildable: true,
        deployable: true,
        missingDependencies: [],
        configIssues: [],
      };
    } catch (error) {
      throw new WorkspaceError(
        `Failed to get project status: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'PROJECT_STATUS_FAILED',
        { projectName, error },
      );
    }
  }

  // Private helper methods

  /**
   * Extract creation context from session
   */
  private extractCreationContext(session: WebsiteSession): WebsiteCreationContext {
    // Get the latest user message
    const userMessages = session.context.messages.filter(m => m.role === 'user');
    const latestMessage = userMessages[userMessages.length - 1];

    const prompt = latestMessage?.content || 'Create a website';
    const projectName = this.generateProjectName(prompt);

    return {
      prompt,
      projectName,
      preferences: session.context.preferences,
      stack: Array.isArray(session.context.data?.stack) ? session.context.data.stack : [],
      features: Array.isArray(session.context.data?.features) ? session.context.data.features : [],
    };
  }

  /**
   * Extract edit context from session
   */
  private extractEditContext(session: WebsiteSession): WebsiteEditContext {
    // Get the latest user message
    const userMessages = session.context.messages.filter(m => m.role === 'user');
    const latestMessage = userMessages[userMessages.length - 1];

    return {
      instructions: latestMessage?.content || 'Edit the website',
      currentContent:
        typeof session.context.data?.currentContent === 'string'
          ? session.context.data.currentContent
          : undefined,
      targetFiles: Array.isArray(session.context.data?.targetFiles)
        ? session.context.data.targetFiles
        : [],
      editType:
        typeof session.context.data?.editType === 'string'
          ? (session.context.data.editType as 'content' | 'code' | 'structure')
          : 'code',
    };
  }

  /**
   * Create workspace for session
   */
  private async createWorkspace(
    session: WebsiteSession,
    context: WebsiteCreationContext,
  ): Promise<Workspace> {
    const workspaceOptions = {
      user: session.user,
      type: 'temporary' as const,
      project: {
        name: context.projectName,
        description: context.prompt,
        type: 'static' as const,
        stack: {
          framework: 'vanilla',
          styling: 'css',
          buildTool: 'none',
          packageManager: 'npm',
          runtime: 'browser',
          dependencies: [],
        },
        config: {
          srcDir: 'src',
          buildDir: 'dist',
          publicDir: 'public',
          entryPoint: 'index.html',
          configFiles: [],
          env: {},
        },
      },
      metadata: {
        sessionId: session.id,
        operation: session.operation,
      },
    };

    return await this.workspaceManager.createWorkspace(workspaceOptions);
  }

  /**
   * Get or create workspace for session
   */
  private async getOrCreateWorkspace(session: WebsiteSession): Promise<Workspace> {
    // Check if workspace already exists in session
    const existingWorkspaceId = session.context.data?.workspace?.id;
    if (existingWorkspaceId) {
      const workspace = await this.workspaceManager.getWorkspace(existingWorkspaceId);
      if (workspace) {
        return workspace;
      }
    }

    // Create new workspace
    const context = this.extractCreationContext(session);
    return await this.createWorkspace(session, context);
  }

  /**
   * Write generated files to workspace
   */
  private async writeFilesToWorkspace(
    workspace: Workspace,
    files: Array<{ path: string; content: string }>,
  ): Promise<void> {
    for (const file of files) {
      await this.workspaceManager.writeWorkspaceFile(workspace, file.path, file.content);
    }
  }

  /**
   * Commit initial code to repository
   */
  private async commitInitialCode(
    repositoryUrl: string,
    files: Array<{ path: string; content: string }>,
  ): Promise<void> {
    const fileOperations: FileOperation[] = files.map(file => ({
      path: file.path,
      content: file.content,
      operation: 'create',
    }));

    await this.updateRepository(repositoryUrl, fileOperations);
  }

  /**
   * Validate repository
   */
  private async validateRepository(repoInfo: RepositoryInfo): Promise<RepositoryValidation> {
    try {
      if (!repoInfo.owner || !repoInfo.repo) {
        return {
          isValid: false,
          error: 'Invalid repository format',
        };
      }

      // Check if repository exists
      const response = await this.github.repos.get({
        owner: repoInfo.owner,
        repo: repoInfo.repo,
      });

      return {
        isValid: true,
        repoData: {
          fullName: (response.data as any).full_name as string,
          description: (response.data as any).description as string,
          htmlUrl: (response.data as any).html_url as string,
          cloneUrl: (response.data as any).clone_url as string,
          defaultBranch: (response.data as any).default_branch as string,
          createdAt: (response.data as any).created_at as string,
          updatedAt: (response.data as any).updated_at as string,
        },
      };
    } catch (error: unknown) {
      return {
        isValid: false,
        error: `Repository validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      };
    }
  }

  /**
   * Get repository metadata
   */
  private async getRepositoryMetadata(repoInfo: RepositoryInfo): Promise<RepositoryMetadata> {
    try {
      const response = await this.github.repos.get({
        owner: repoInfo.owner!,
        repo: repoInfo.repo!,
      });

      return {
        fullName: (response.data as any).full_name as string,
        name: (response.data as any).name as string,
        description: ((response.data as any).description as string) || '',
        htmlUrl: (response.data as any).html_url as string,
        cloneUrl: (response.data as any).clone_url as string,
        defaultBranch: (response.data as any).default_branch as string,
        createdAt: (response.data as any).created_at as string,
        updatedAt: (response.data as any).updated_at as string,
        latestCommitSha: null, // Would need additional API call
      };
    } catch (error: unknown) {
      throw new GitError(
        `Failed to get repository metadata: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'REPOSITORY_METADATA_FAILED',
        { repoInfo, error },
      );
    }
  }

  /**
   * Get repository content
   */
  private async getRepositoryContent(repoInfo: RepositoryInfo): Promise<string> {
    try {
      // Get the main file (index.html, README.md, etc.)
      const files = ['index.html', 'README.md', 'main.html', 'home.html'];

      for (const fileName of files) {
        try {
          const response = await this.github.repos.getContent({
            owner: repoInfo.owner!,
            repo: repoInfo.repo!,
            path: fileName,
          });

          if ('content' in response.data) {
            return Buffer.from(response.data.content, 'base64').toString('utf-8');
          }
        } catch {
          // Try next file
          continue;
        }
      }

      // If no main file found, return a default
      return '<html><body><h1>Imported Repository</h1><p>Content will be generated.</p></body></html>';
    } catch (error: unknown) {
      throw new GitError(
        `Failed to get repository content: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'REPOSITORY_CONTENT_FAILED',
        { repoInfo, error },
      );
    }
  }

  /**
   * Generate project name from prompt
   */
  private generateProjectName(prompt: string): string {
    // Simple project name generation
    const words = prompt
      .toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .split(' ')
      .filter(word => word.length > 2)
      .slice(0, 3);

    return words.join('-') || 'website';
  }

  /**
   * Clean up operation resources
   */
  private async cleanupOperation(operationId: string, session: WebsiteSession): Promise<void> {
    try {
      // Cancel any ongoing operations
      const controller = this.activeOperations.get(operationId);
      if (controller) {
        controller.abort();
        this.activeOperations.delete(operationId);
      }

      // Clean up workspace if created
      const workspace = session.context.data?.workspace;
      if (workspace) {
        await this.workspaceManager.cleanupWorkspace(workspace);
      }
    } catch (error: unknown) {
      console.error(`[WebsiteManager] Cleanup failed for operation ${operationId}:`, error);
    }
  }

  /**
   * Cancel operation
   */
  cancelOperation(operationId: string): void {
    const controller = this.activeOperations.get(operationId);
    if (controller) {
      controller.abort();
      this.activeOperations.delete(operationId);
    }
  }

  /**
   * Get active operations
   */
  getActiveOperations(): string[] {
    return Array.from(this.activeOperations.keys());
  }

  /**
   * Shutdown manager
   */
  shutdown(): void {
    // Cancel all active operations
    for (const [, controller] of this.activeOperations) {
      controller.abort();
    }
    this.activeOperations.clear();

    console.log('[WebsiteManager] Shutdown complete');
  }
}

/**
 * WebsiteManager factory function
 */
export function createWebsiteManager(
  config: WebsiteManagerConfig,
  workspaceManager: WorkspaceManager,
  geminiRunner: GeminiRunner,
): WebsiteManager {
  return new WebsiteManager(config, workspaceManager, geminiRunner);
}

/**
 * Default WebsiteManager configuration
 */
export const DEFAULT_WEBSITE_MANAGER_CONFIG: Partial<WebsiteManagerConfig> = {
  workspaceDir: '/tmp/workspaces',
  sessionTimeout: 30 * 60 * 1000, // 30 minutes
  enableGitIntegration: true,
  enableDeployment: true,
};
