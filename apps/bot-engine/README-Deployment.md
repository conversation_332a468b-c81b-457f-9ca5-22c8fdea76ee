# WhatsApp Bot Engine - Fly.io Deployment Guide

## Overview

This guide covers the complete deployment setup for the WhatsApp Bot Engine on Fly.io with comprehensive scaling, monitoring, and production-ready configurations.

## Architecture

```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                Fly.io Infrastructure                                 │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐                │
│  │   Load Balancer  │    │   Auto Scaler   │    │   Health Check   │                │
│  │                 │    │                 │    │                 │                │
│  │  • HTTPS Only   │    │  • CPU Based    │    │  • Liveness     │                │
│  │  • Rate Limit   │    │  • Memory Based │    │  • Readiness    │                │
│  │  • Geo Routing  │    │  • Request Based│    │  • Startup      │                │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘                │
│           │                       │                       │                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                          Application Instances                                  │ │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐                │ │
│  │  │   Instance 1    │  │   Instance 2    │  │   Instance N    │                │ │
│  │  │                 │  │                 │  │                 │                │ │
│  │  │ • Bot Engine    │  │ • Bot Engine    │  │ • Bot Engine    │                │ │
│  │  │ • Webhook       │  │ • Webhook       │  │ • Webhook       │                │ │
│  │  │ • WebSocket     │  │ • WebSocket     │  │ • WebSocket     │                │ │
│  │  │ • Health Checks │  │ • Health Checks │  │ • Health Checks │                │ │
│  │  │ • Monitoring    │  │ • Monitoring    │  │ • Monitoring    │                │ │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘                │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
│           │                       │                       │                        │
│  ┌─────────────────────────────────────────────────────────────────────────────────┐ │
│  │                          Persistent Storage                                     │ │
│  │  ┌─────────────────┐                    ┌─────────────────┐                    │ │
│  │  │   Workspaces    │                    │    Sessions     │                    │ │
│  │  │                 │                    │                 │                    │ │
│  │  │ • 50GB Volume   │                    │ • 20GB Volume   │                    │ │
│  │  │ • Git Repos     │                    │ • User Data     │                    │ │
│  │  │ • Build Cache   │                    │ • Temp Files    │                    │ │
│  │  └─────────────────┘                    └─────────────────┘                    │ │
│  └─────────────────────────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## Features

### ✅ Production-Ready Configuration
- **Auto-scaling**: CPU/memory/request based scaling (1-5 instances)
- **Zero-downtime deployments**: Rolling deployment strategy
- **Health checks**: Comprehensive health monitoring
- **Performance optimization**: Connection pooling, caching
- **Security**: Rate limiting, security headers, HTTPS only

### ✅ Monitoring & Observability
- **Application metrics**: Request rate, response time, error rate
- **System metrics**: CPU, memory, disk usage
- **Business metrics**: Sessions, websites, deployments
- **Prometheus integration**: Metrics export
- **Grafana dashboards**: Visual monitoring
- **Alerting**: Configurable alerts and notifications

### ✅ Deployment Pipeline
- **Staging environment**: Safe testing before production
- **Validation checks**: Health checks, smoke tests
- **Rollback capability**: Automatic rollback on failures
- **Environment management**: Secure secrets handling

## Prerequisites

### Required Tools
```bash
# Install Fly.io CLI
curl -L https://fly.io/install.sh | sh

# Install Docker
# Follow Docker installation guide for your OS

# Install dependencies
npm install -g pnpm
brew install jq curl
```

### Required Environment Variables
```bash
# Twilio Configuration
export TWILIO_ACCOUNT_SID="your_account_sid"
export TWILIO_AUTH_TOKEN="your_auth_token"
export TWILIO_PHONE_NUMBER="your_phone_number"

# Google/Gemini Configuration
export GOOGLE_API_KEY="your_google_api_key"

# GitHub Configuration
export GITHUB_TOKEN="your_github_token"
export GITHUB_ORGANIZATION="your_organization" # Optional

# Vercel Configuration
export VERCEL_TOKEN="your_vercel_token"
export VERCEL_TEAM_ID="your_team_id" # Optional

# Optional Configuration
export WEBHOOK_SECRET_KEY="your_webhook_secret"
export ALERT_WEBHOOK_URL="your_alert_webhook_url"
```

## Quick Start

### 1. Initial Setup
```bash
# Clone the repository
git clone <repository-url>
cd whatsite-bot

# Install dependencies
pnpm install

# Login to Fly.io
fly auth login

# Create Fly.io app (if not exists)
fly launch --no-deploy
```

### 2. Deploy to Staging
```bash
# Deploy to staging environment
pnpm --filter bot-engine deploy:staging

# Or use the script directly
./scripts/deploy.sh staging
```

### 3. Deploy to Production
```bash
# Deploy to production (includes staging validation)
pnpm --filter bot-engine deploy:production

# Or skip staging validation
pnpm --filter bot-engine deploy:quick
```

### 4. Set Up Monitoring
```bash
# Set up comprehensive monitoring
pnpm --filter bot-engine monitoring:setup

# Or use the script directly
./scripts/monitoring-setup.sh
```

## Deployment Configurations

### Configuration Files
- **`fly.toml`**: Base configuration
- **`fly.production.toml`**: Production-optimized settings
- **`Dockerfile`**: Multi-stage build for optimization

### Scaling Configuration
```toml
[machine.auto_scaling]
min_instances = 1
max_instances = 5

[machine.auto_scaling.metrics.cpu]
target = 70.0
scale_up_threshold = 80.0
scale_down_threshold = 40.0

[machine.auto_scaling.metrics.memory]
target = 75.0
scale_up_threshold = 85.0
scale_down_threshold = 50.0
```

### Health Check Configuration
```toml
[[http_service.checks]]
interval = "10s"
timeout = "5s"
grace_period = "10s"
method = "GET"
path = "/health"
protocol = "http"
restart_limit = 3
```

## Monitoring & Observability

### Health Check Endpoints
- **`/health`**: Comprehensive health check
- **`/ready`**: Readiness check for load balancing
- **`/startup`**: Startup progress monitoring
- **`/live`**: Simple liveness check
- **`/metrics`**: Prometheus metrics

### Key Metrics
- **Request Metrics**: `http_requests_total`, `http_requests_duration_seconds`
- **Session Metrics**: `sessions_active`, `sessions_created_total`
- **Business Metrics**: `websites_created_total`, `webhooks_processed_total`
- **System Metrics**: `process_memory_bytes`, `process_cpu_seconds_total`

### Alerting Rules
- **High Error Rate**: Error rate > 5% for 5 minutes
- **High Memory Usage**: Memory > 1.5GB for 5 minutes
- **Session Failures**: > 10 failed sessions in 5 minutes
- **Webhook Delays**: P95 processing time > 30 seconds

## Operational Commands

### Deployment Commands
```bash
# Deploy to staging
pnpm --filter bot-engine deploy:staging

# Deploy to production
pnpm --filter bot-engine deploy:production

# Quick production deploy (skip staging)
pnpm --filter bot-engine deploy:quick

# Direct Fly.io deployment
pnpm --filter bot-engine fly:deploy
```

### Monitoring Commands
```bash
# Check health
pnpm --filter bot-engine health:check

# Check readiness
pnpm --filter bot-engine health:ready

# Check startup status
pnpm --filter bot-engine health:startup

# Get metrics
pnpm --filter bot-engine metrics
```

### Fly.io Commands
```bash
# View application status
pnpm --filter bot-engine fly:status

# View logs
pnpm --filter bot-engine fly:logs

# Scale application
pnpm --filter bot-engine fly:scale

# Manage secrets
pnpm --filter bot-engine fly:secrets
```

## Production Checklist

### Pre-Deployment
- [ ] Environment variables configured
- [ ] Secrets properly set
- [ ] Health checks passing locally
- [ ] Tests passing
- [ ] Monitoring configured

### Deployment
- [ ] Staging deployment successful
- [ ] Health checks passing
- [ ] Smoke tests passing
- [ ] Performance tests passing
- [ ] Security scans completed

### Post-Deployment
- [ ] Health checks stable
- [ ] Metrics collecting properly
- [ ] Alerts configured
- [ ] Monitoring dashboards working
- [ ] Rollback plan tested

## Troubleshooting

### Common Issues

#### Deployment Failures
```bash
# Check deployment logs
fly logs

# Check health status
curl https://whatsite-bot-engine.fly.dev/health

# Check application status
fly status
```

#### Health Check Failures
```bash
# Check detailed health status
curl -s https://whatsite-bot-engine.fly.dev/health | jq

# Check readiness
curl -s https://whatsite-bot-engine.fly.dev/ready | jq

# Check startup progress
curl -s https://whatsite-bot-engine.fly.dev/startup | jq
```

#### Performance Issues
```bash
# Check metrics
curl -s https://whatsite-bot-engine.fly.dev/metrics

# Check scaling status
fly scale show

# Check machine resources
fly machine list
```

### Rollback Procedure
```bash
# Automatic rollback (if enabled)
# Rollback is triggered automatically on deployment failure

# Manual rollback
fly releases list
fly releases rollback <version>
```

## Security Considerations

### Network Security
- HTTPS only (enforced)
- Rate limiting enabled
- Security headers configured
- CORS properly configured

### Application Security
- Webhook signature validation
- Input validation
- Error handling
- Secure logging

### Data Security
- Encrypted volumes
- Secure environment variables
- Access control
- Regular security updates

## Performance Optimization

### Application Performance
- Connection pooling
- Caching strategies
- Request optimization
- Memory management

### Infrastructure Performance
- Auto-scaling configuration
- Load balancing
- Regional optimization
- Resource allocation

## Maintenance

### Regular Tasks
- Monitor metrics and alerts
- Review logs for errors
- Update dependencies
- Security patches
- Performance optimization

### Scheduled Maintenance
- Volume backups
- Log rotation
- Metrics cleanup
- Health check validation

## Support

### Documentation
- [Fly.io Documentation](https://fly.io/docs/)
- [Bot Engine README](./README.md)
- [WebsiteManager README](./README-WebsiteManager.md)

### Monitoring URLs
- Health Check: `https://whatsite-bot-engine.fly.dev/health`
- Metrics: `https://whatsite-bot-engine.fly.dev/metrics`
- Status: `https://whatsite-bot-engine.fly.dev/ready`

### Emergency Contacts
- Deployment issues: Check deployment logs
- Performance issues: Check monitoring dashboards
- Security issues: Review security logs