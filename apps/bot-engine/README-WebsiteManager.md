# WebsiteManager Implementation

This document describes the WebsiteManager implementation for the WhatsApp Website Bot system.

## Overview

The WebsiteManager is a comprehensive class that handles all website-related operations including:

- **Website Creation**: Generate new websites from user prompts using AI
- **Website Editing**: Modify existing websites based on user instructions
- **Repository Management**: Create, clone, and manage GitHub repositories
- **Deployment Operations**: Deploy websites to Vercel with automated configuration
- **Integration Layer**: Coordinate between WorkspaceManager and GeminiRunner
- **Error Handling**: Comprehensive error management and recovery

## Architecture

```
WebsiteManager
├── WorkspaceManager (file system operations)
├── GeminiRunner (AI code generation)
├── GitHub API (repository management)
├── Vercel API (deployment)
└── Session Management (user context)
```

## Key Features

### 1. Website Creation
- AI-powered code generation from natural language prompts
- Automatic project structure creation
- GitHub repository creation with initial commit
- Vercel deployment with custom domain support
- Session state management throughout the process

### 2. Website Editing
- Context-aware code modifications
- Incremental updates to existing projects
- Repository synchronization
- Automatic redeployment

### 3. Repository Operations
- GitHub repository creation and management
- Repository cloning and content extraction
- File operations (create, update, delete)
- Branch management and version control

### 4. Deployment System
- Vercel integration with automatic deployments
- Custom domain configuration
- Build process management
- Deployment status tracking

### 5. Error Handling
- Comprehensive error classification
- Automatic retry mechanisms
- Graceful degradation
- User-friendly error messages

## Usage Example

```typescript
import { WebsiteManager, WebsiteManagerConfig } from './website-manager';

// Configuration
const config: WebsiteManagerConfig = {
  github: {
    token: process.env.GITHUB_TOKEN,
    organization: 'my-org'
  },
  vercel: {
    token: process.env.VERCEL_TOKEN,
    teamId: 'my-team'
  },
  workspaceDir: '/tmp/workspaces',
  sessionTimeout: 30 * 60 * 1000,
  enableGitIntegration: true,
  enableDeployment: true
};

// Initialize manager
const websiteManager = new WebsiteManager(
  config,
  workspaceManager,
  geminiRunner
);

// Create website
const result = await websiteManager.createWebsite(session);
```

## API Reference

### WebsiteManager Class

#### Constructor
```typescript
constructor(
  config: WebsiteManagerConfig,
  workspaceManager: WorkspaceManager,
  geminiRunner: GeminiRunner
)
```

#### Methods

##### `createWebsite(session: WebsiteSession): Promise<DeploymentResult>`
Creates a new website from user session context.

**Parameters:**
- `session`: WebsiteSession containing user context and requirements

**Returns:**
- `DeploymentResult`: Result containing deployment URL and metadata

**Process:**
1. Extract creation context from session
2. Create isolated workspace
3. Initialize AI runner
4. Generate website code
5. Create GitHub repository
6. Deploy to Vercel
7. Update session with results

##### `editWebsite(session: WebsiteSession): Promise<DeploymentResult>`
Edits an existing website based on user instructions.

**Parameters:**
- `session`: WebsiteSession containing edit instructions

**Returns:**
- `DeploymentResult`: Result containing updated deployment URL

**Process:**
1. Extract edit context from session
2. Get existing workspace
3. Generate updated code
4. Update repository
5. Redeploy to Vercel

##### `importRepository(repoInfo: RepositoryInfo): Promise<ImportResult>`
Imports an existing GitHub repository.

**Parameters:**
- `repoInfo`: Repository information including owner, repo, branch

**Returns:**
- `ImportResult`: Result containing project details and deployment URL

##### `deployToVercel(code: string, projectName: string): Promise<string>`
Deploys code to Vercel platform.

**Parameters:**
- `code`: Website code to deploy
- `projectName`: Name for the project

**Returns:**
- `string`: Deployment URL

##### `createGitHubRepository(name: string, description: string): Promise<string>`
Creates a new GitHub repository.

**Parameters:**
- `name`: Repository name
- `description`: Repository description

**Returns:**
- `string`: Repository URL

##### `getProjectStatus(projectName: string): Promise<ProjectStatus>`
Gets the status of a project.

**Parameters:**
- `projectName`: Name of the project

**Returns:**
- `ProjectStatus`: Project status information

## Configuration

### WebsiteManagerConfig

```typescript
interface WebsiteManagerConfig {
  github: {
    token: string;
    organization?: string;
    baseUrl?: string;
  };
  vercel: {
    token: string;
    teamId?: string;
    projectId?: string;
  };
  workspaceDir: string;
  sessionTimeout: number;
  enableGitIntegration: boolean;
  enableDeployment: boolean;
}
```

### Environment Variables

```bash
# GitHub Configuration
GITHUB_TOKEN=your_github_token
GITHUB_ORG=your_organization

# Vercel Configuration
VERCEL_TOKEN=your_vercel_token
VERCEL_TEAM_ID=your_team_id

# Workspace Configuration
WORKSPACE_DIR=/app/workspaces
SESSION_TIMEOUT=1800000
```

## Error Handling

The WebsiteManager includes comprehensive error handling:

### Error Types
- `WorkspaceError`: Workspace-related operations
- `GitError`: Git and repository operations
- `DeploymentError`: Deployment-related failures
- `AIError`: AI service issues
- `ValidationError`: Input validation failures

### Error Recovery
- Automatic retry for transient failures
- Graceful degradation for non-critical services
- Comprehensive cleanup on failures
- User-friendly error messages

### Example Error Handling

```typescript
try {
  const result = await websiteManager.createWebsite(session);
} catch (error) {
  if (error instanceof DeploymentError) {
    // Handle deployment-specific error
    console.error('Deployment failed:', error.getUserMessage());
  } else if (error instanceof AIError) {
    // Handle AI service error
    console.error('AI service error:', error.getUserMessage());
  } else {
    // Handle generic error
    console.error('Unknown error:', error.message);
  }
}
```

## Session Integration

The WebsiteManager integrates seamlessly with the session management system:

### Session Context
- User information and preferences
- Message history and context
- Project state and metadata
- Streaming narrative updates

### Session Updates
- Real-time progress updates
- Narrative item logging
- Error state management
- Result persistence

## Dependencies

### Required Packages
- `@octokit/rest`: GitHub API client
- `crypto`: UUID generation
- `path`: File path utilities

### Internal Dependencies
- `WorkspaceManager`: File system operations
- `GeminiRunner`: AI code generation
- `SessionManager`: Session state management

## Testing

### Unit Tests
```typescript
// Test website creation
describe('WebsiteManager', () => {
  it('should create website successfully', async () => {
    const result = await websiteManager.createWebsite(mockSession);
    expect(result.success).toBe(true);
    expect(result.url).toBeDefined();
  });

  it('should handle creation errors gracefully', async () => {
    const result = await websiteManager.createWebsite(invalidSession);
    expect(result.success).toBe(false);
    expect(result.errors).toContain('Invalid session');
  });
});
```

### Integration Tests
```typescript
// Test full workflow
describe('WebsiteManager Integration', () => {
  it('should complete full create-edit-deploy cycle', async () => {
    // Create website
    const createResult = await websiteManager.createWebsite(session);
    expect(createResult.success).toBe(true);

    // Edit website
    const editResult = await websiteManager.editWebsite(session);
    expect(editResult.success).toBe(true);

    // Verify deployment
    const response = await fetch(editResult.url);
    expect(response.ok).toBe(true);
  });
});
```

## Performance Considerations

### Optimization Strategies
- Workspace caching for repeated operations
- Async/await for non-blocking operations
- Batch operations for multiple file updates
- Connection pooling for API calls

### Resource Management
- Automatic cleanup of temporary resources
- Memory management for large files
- Connection timeout handling
- Rate limiting compliance

## Security

### Authentication
- GitHub token-based authentication
- Vercel API token validation
- Session-based access control

### Data Protection
- Secure handling of user data
- Temporary file cleanup
- API key encryption
- Access logging

## Monitoring and Logging

### Operational Metrics
- Creation success/failure rates
- Deployment times
- Error frequencies
- Resource usage

### Logging Strategy
- Structured logging with context
- Error tracking and alerting
- Performance monitoring
- User activity logging

## Future Enhancements

### Planned Features
- Multi-cloud deployment support
- Advanced AI model integration
- Real-time collaboration features
- Template marketplace
- Performance optimization tools

### Scalability Improvements
- Horizontal scaling support
- Database integration
- Caching layer
- Load balancing

## Support and Maintenance

### Troubleshooting
- Common error scenarios and solutions
- Performance debugging guides
- Configuration validation tools
- Health check endpoints

### Updates and Migrations
- Version compatibility matrix
- Migration procedures
- Breaking change notifications
- Rollback strategies

## Contributing

### Development Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run tests: `npm test`
5. Start development server: `npm run dev`

### Code Standards
- TypeScript strict mode
- ESLint configuration
- Prettier formatting
- Comprehensive test coverage

For more information, see the [main README](./README.md) and [SessionManager documentation](./README-SessionManager.md).